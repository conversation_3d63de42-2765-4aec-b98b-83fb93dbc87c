# 空数组显示解决方案

## 方案概述

保持 `selectedOrderList` 默认为空数组，但通过计算属性和特殊处理逻辑确保界面上始终显示至少一个开票订单选择框。

## 核心实现

### 1. 计算属性 `displayOrderList`
```typescript
// 显示用的订单列表（确保至少有一个初始项）
displayOrderList: computed(() => {
  if (state.formData.selectedOrderList.length === 0) {
    return [0]; // 当数组为空时，显示一个初始项
  }
  return state.formData.selectedOrderList;
})
```

### 2. 选择变化处理方法
```typescript
/**
 * 处理订单选择变化
 * @param index 索引
 * @param value 选择的值
 */
handleOrderSelectChange(index: number, value: number) {
  // 如果数组为空，先初始化
  if (state.formData.selectedOrderList.length === 0) {
    state.formData.selectedOrderList = [value];
  } else {
    // 确保数组有足够的长度
    while (state.formData.selectedOrderList.length <= index) {
      state.formData.selectedOrderList.push(0);
    }
    state.formData.selectedOrderList[index] = value;
  }
}
```

### 3. 模板绑定逻辑
```vue
<m-form-item
  v-for="(orderItem, index) in displayOrderList"
  :key="index"
  :label="index === 0 ? '选择开票订单' : ''"
  :name="formData.selectedOrderList.length === 0 ? 'tempOrderSelect' : ['selectedOrderList', index]"
>
  <m-select
    :value="formData.selectedOrderList.length === 0 ? 0 : formData.selectedOrderList[index]"
    @update:value="(value) => handleOrderSelectChange(index, value)"
    :options="historyOrderList"
    placeholder="请选择开票订单"
  />
  <m-button
    v-if="displayOrderList.length > 1 || formData.selectedOrderList.length > 0"
    @click="removeOrderSelectItem(index)"
  >
    删除
  </m-button>
</m-form-item>
```

## 方案优势

### 1. 数据一致性
- ✅ `selectedOrderList` 始终保持为真实的用户选择数据
- ✅ 空数组表示用户尚未进行任何选择
- ✅ 提交时数据结构清晰明确

### 2. 用户体验
- ✅ 界面上始终显示至少一个选择框
- ✅ 用户无需额外操作就能看到选择界面
- ✅ 删除操作符合用户预期

### 3. 逻辑清晰
- ✅ 显示逻辑与数据逻辑分离
- ✅ 通过计算属性处理显示状态
- ✅ 特殊处理方法确保数据正确性

## 关键处理逻辑

### 空数组状态
- **显示**：通过 `displayOrderList` 显示一个初始项
- **表单名称**：使用临时名称 `tempOrderSelect` 避免验证冲突
- **删除按钮**：不显示删除按钮（因为实际数组为空）

### 有数据状态
- **显示**：直接显示 `selectedOrderList` 的内容
- **表单名称**：使用正常的数组字段名称 `['selectedOrderList', index]`
- **删除按钮**：根据数组长度决定是否显示

### 添加操作
```typescript
addOrderSelectItem() {
  // 如果是空数组，先添加第一个项，再添加新项
  if (state.formData.selectedOrderList.length === 0) {
    state.formData.selectedOrderList.push(0);
  }
  state.formData.selectedOrderList.push(0);
}
```

### 删除操作
```typescript
removeOrderSelectItem(index: number) {
  if (state.formData.selectedOrderList.length > 1) {
    state.formData.selectedOrderList.splice(index, 1);
  } else {
    // 如果只有一个项，清空数组（通过计算属性会自动显示一个初始项）
    state.formData.selectedOrderList = [];
  }
}
```

## 验证处理

- **空数组时**：使用临时字段名，不进行实际验证
- **有数据时**：正常进行数组字段验证
- **提交时**：检查实际的 `selectedOrderList` 数组

这个方案完美解决了保持空数组默认值的同时确保用户界面友好的需求。
