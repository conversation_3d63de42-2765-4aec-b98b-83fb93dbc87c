# 空数组显示 Placeholder 解决方案

## 方案概述

保持 `selectedOrderList` 默认为空数组，当数组为空时显示一个没有默认值的下拉框，只显示 placeholder 文本。

## 核心实现

### 1. 计算属性 `displayOrderList`
```typescript
// 显示用的订单列表（确保至少有一个空的下拉框）
displayOrderList: computed(() => {
  if (state.formData.selectedOrderList.length === 0) {
    return [undefined]; // 当数组为空时，显示一个无默认值的下拉框
  }
  return state.formData.selectedOrderList;
})
```

### 2. 模板值绑定
```vue
<m-select
  :value="formData.selectedOrderList.length === 0 ? undefined : formData.selectedOrderList[index]"
  @update:value="(value) => handleOrderSelectChange(index, value)"
  :options="historyOrderList"
  placeholder="请选择开票订单"
  class="invoice-order-select"
/>
```

### 3. 验证规则调整
```typescript
selectedOrderList: [
  {
    required: state.isOldOrder,
    validator(rule: any, value: number) {
      if (!state.isOldOrder) {
        return Promise.resolve();
      }
      // 当数组为空时，检查是否有选择
      if (state.formData.selectedOrderList.length === 0) {
        return Promise.reject('请选择开票订单');
      }
      if (!value || value === 0) {
        return Promise.reject('请选择开票订单');
      }
      return Promise.resolve();
    }
  }
]
```

## 用户交互流程

### 初始状态（空数组）
- **显示**：一个显示 placeholder "请选择开票订单" 的下拉框
- **值**：`undefined`（无默认值）
- **删除按钮**：不显示
- **验证**：使用临时字段名 `tempOrderSelect`

### 用户选择后
- **数据变化**：空数组变为 `[selectedValue]`
- **显示**：正常显示选中的值
- **删除按钮**：不显示（只有一个项）
- **验证**：使用正常的数组字段验证

### 添加更多项
- **数据变化**：数组增加新项 `[value1, 0, ...]`
- **显示**：显示多个下拉框
- **删除按钮**：显示（多个项时）

### 删除到最后一项
- **数据变化**：数组变为空 `[]`
- **显示**：回到初始状态，显示 placeholder
- **删除按钮**：不显示

## 关键特性

### 1. 无默认值
- ✅ 空数组时下拉框显示 placeholder
- ✅ 没有预设的默认选项
- ✅ 用户必须主动选择

### 2. 数据一致性
- ✅ 空数组表示用户未进行任何选择
- ✅ 有数据时反映真实的用户选择
- ✅ 提交时数据结构清晰

### 3. 用户体验
- ✅ 始终显示至少一个选择框
- ✅ Placeholder 提供清晰的操作提示
- ✅ 删除操作符合预期

### 4. 验证逻辑
- ✅ 空数组时检查是否有选择
- ✅ 有数据时正常验证每个选项
- ✅ 避免验证冲突

## 技术细节

### 显示逻辑
```typescript
// 空数组 -> 显示 [undefined]
// 有数据 -> 显示实际数据
displayOrderList: computed(() => {
  return state.formData.selectedOrderList.length === 0 
    ? [undefined] 
    : state.formData.selectedOrderList;
})
```

### 值绑定逻辑
```vue
<!-- 空数组时绑定 undefined，有数据时绑定实际值 -->
:value="formData.selectedOrderList.length === 0 ? undefined : formData.selectedOrderList[index]"
```

### 删除按钮显示逻辑
```vue
<!-- 只有在有实际数据且多于一项时才显示删除按钮 -->
v-if="displayOrderList.length > 1 || formData.selectedOrderList.length > 0"
```

这个方案完美实现了空数组时显示 placeholder 的需求，同时保持了良好的用户体验和数据一致性。
