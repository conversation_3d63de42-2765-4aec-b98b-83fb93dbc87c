# 修复 Form.Item 警告问题

## 问题描述
出现警告：`[ant-design-vue: Form.Item] FormItem can only collect one field item, you haved set ASelect, ASelect 2 field items.`

这个警告是因为在一个 `m-form-item` 中包含了多个表单控件（多个 `m-select`），而 Ant Design Vue 的表单项只能收集一个字段。

## 解决方案

### 1. 重构表单结构
将原来的包装式结构改为每个下拉框都有独立的 `m-form-item`：

```vue
<!-- 修改前：一个 form-item 包含多个 select -->
<m-form-item name="selectedOrderList" :rules="...">
  <div v-for="..." >
    <m-select v-model:value="formData.selectedOrderList[index]" />
    <m-select v-model:value="formData.selectedOrderList[index2]" />
  </div>
</m-form-item>

<!-- 修改后：每个 select 都有独立的 form-item -->
<m-form-item
  v-for="(orderItem, index) in formData.selectedOrderList"
  :key="index"
  :label="index === 0 ? '选择开票订单' : ''"
  :name="['selectedOrderList', index]"
  :rules="index === 0 ? specialInvoiceRules.selectedOrderList : specialInvoiceRules.selectedOrderItem"
>
  <div class="invoice-order-row">
    <m-select v-model:value="formData.selectedOrderList[index]" />
    <m-form-item-rest>
      <m-button @click="removeOrderSelectItem(index)">删除</m-button>
    </m-form-item-rest>
  </div>
</m-form-item>
```

### 2. 使用 m-form-item-rest
对于不需要被表单收集的元素（如删除按钮、添加按钮），使用 `m-form-item-rest` 包装：

```vue
<m-form-item-rest>
  <m-button type="dashed" @click="addOrderSelectItem">
    添加开票订单
  </m-button>
</m-form-item-rest>
```

### 3. 分离验证规则
为第一个项目和其他项目创建不同的验证规则：

```typescript
// 第一个开票订单的验证规则
selectedOrderList: [
  {
    required: state.isOldOrder,
    validator(rule: any, value: number) {
      if (!state.isOldOrder) {
        return Promise.resolve();
      }
      if (!value || value === 0) {
        return Promise.reject('请选择开票订单');
      }
      return Promise.resolve();
    }
  }
],
// 其他开票订单的验证规则
selectedOrderItem: [
  {
    required: state.isOldOrder,
    validator(rule: any, value: number) {
      if (!state.isOldOrder) {
        return Promise.resolve();
      }
      if (!value || value === 0) {
        return Promise.reject('请选择开票订单');
      }
      return Promise.resolve();
    }
  }
]
```

## 修改效果

### 解决的问题
- ✅ 消除了 Form.Item 警告
- ✅ 每个下拉框都有独立的验证
- ✅ 保持了原有的功能和用户体验

### 表单结构
- 每个开票订单下拉框都是独立的 `m-form-item`
- 第一个下拉框显示标签"选择开票订单"
- 其他下拉框不显示标签
- 删除按钮和添加按钮使用 `m-form-item-rest` 包装

### 验证行为
- 只在 `isOldOrder = true` 时进行验证
- 每个下拉框都会独立验证是否已选择
- 验证消息统一为"请选择开票订单"

## 注意事项

1. **数组字段名称**：使用 `:name="['selectedOrderList', index]"` 来正确绑定数组字段
2. **条件验证**：验证规则中都包含 `isOldOrder` 的检查
3. **用户体验**：保持了原有的添加/删除功能和视觉效果

这个修改完全解决了 Ant Design Vue 的表单项警告问题，同时保持了所有原有功能。
