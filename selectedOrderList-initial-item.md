# selectedOrderList 初始项设置

## 修改内容

确保 `selectedOrderList` 默认包含一个初始项，而不是空数组，这样用户在老订单模式下总是能看到至少一个开票订单选择框。

## 修改位置

### 1. 初始状态设置
```typescript
// 表单数据
formData: {
  invoiceAmount: 0,
  selectedOrderList: [0], // 默认包含一个初始项
  buyerType: BuyerTypeEnum.COMPANY,
  // ...其他字段
}
```

### 2. showOldOrder() 方法
```typescript
// 老订单弹窗
showOldOrder() {
  state.orderNos = [];
  state.isOldOrder = true;
  
  // 重置选择的订单列表，确保至少有一个初始项
  state.formData.selectedOrderList = [0];

  methods.getOldOrderList();

  state.visible = true;
}
```

## 修改效果

### 用户体验改进
- ✅ 老订单模式下默认显示一个开票订单选择框
- ✅ 用户不需要先点击"添加"按钮就能看到选择框
- ✅ 界面更加直观，符合用户预期

### 功能保持
- ✅ 添加功能正常：可以继续添加更多选择框
- ✅ 删除功能正常：当有多个选择框时可以删除
- ✅ 验证功能正常：第一个选择框会进行必填验证

## 技术细节

### 初始值说明
- `[0]`：包含一个值为 0 的初始项
- 值为 0 表示"未选择"状态
- 验证器会检查这个值并要求用户选择有效的订单

### 重置逻辑
- 在 `showOldOrder()` 中重置为 `[0]`
- 确保每次打开老订单弹窗时都有一个初始选择框
- 避免因为之前的操作导致选择框数量不正确

## 验证行为

### 第一个选择框
- 显示标签："选择开票订单"
- 应用验证规则：`specialInvoiceRules.selectedOrderList`
- 必须选择有效的订单（不能为 0）

### 其他选择框
- 不显示标签
- 应用验证规则：`specialInvoiceRules.selectedOrderItem`
- 同样必须选择有效的订单

## 最佳实践

1. **默认状态**：为用户提供合理的默认状态
2. **一致性**：确保每次打开弹窗时的状态一致
3. **用户友好**：减少用户的操作步骤

这个修改确保了用户在老订单模式下总是能看到至少一个开票订单选择框，提升了用户体验。
