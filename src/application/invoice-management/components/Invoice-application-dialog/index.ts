import { computed, defineComponent, reactive, ref, toRefs } from 'vue';
import { MUtils, PhoneRegExp } from '@paas/paas-library';
import { type FormInstance } from 'ant-design-vue';
import { ExclamationCircleFilled, PlusOutlined } from '@ant-design/icons-vue';
import {
	BANK_ACCOUNT_PATTERN,
	BANK_NAME_PATTERN,
	BuyerTypeEnum,
	EMAIL_PATTERN,
	HEADER_TYPE_OPTIONS,
	INVOICE_TYPE_OPTIONS,
	InvoiceTypeEnum,
	MAX_ADDRESS_LENGTH,
	PHONE_PATTERN
} from '../../constants';
import { SubmitInvoiceApplicationStore } from '../../store';
import type { ApplyInvoicePreviewResponse, InvoiceApplicationFormModel } from '../../types';
import { StateModel } from '@/application/invoice-management/components/Invoice-application-dialog/types';
import { GetHistoryOrderListStore } from '@/application/invoice-management/components/Invoice-application-dialog/store';

export default defineComponent({
	name: 'InvoiceApplicationDialog',
	components: {
		PlusOutlined,
		ExclamationCircleFilled
	},
	emits: ['close', 'success'],
	setup(props, { emit }) {
		const formRef = ref<FormInstance>();

		const state = reactive<StateModel>({
			visible: false,
			submitLoading: false,
			// 订单号列表
			orderNos: [],
			// 是否是2021年1月1日之前购买的老订单
			isOldOrder: false,
			// 历史订单列表
			historyOrderList: [],
			// 表单数据
			formData: {
				invoiceAmount: 0,
				selectedOrderList: [], // 默认为空数组
				buyerType: BuyerTypeEnum.COMPANY,
				invoiceType: InvoiceTypeEnum.ELECTRONIC_COMMON,
				buyerName: '',
				buyerIdNum: '',
				receiveEmail: '',
				buyerBankName: '',
				buyerBankAccount: '',
				buyerAddress: '',
				buyerMobileNum: '',
				orderNos: []
			}
		});

		// 计算属性
		const computeds = {
			// 是否为专用发票
			isSpecialInvoice: computed(() => {
				return state.formData.invoiceType === InvoiceTypeEnum.ELECTRONIC_SPECIAL;
			}),

			// 可用的发票类型选项
			availableInvoiceTypes: computed(() => {
				// 若抬头类型为个人，只能选择普通发票
				if (state.formData.buyerType === BuyerTypeEnum.PERSONAL) {
					return INVOICE_TYPE_OPTIONS.filter(option => option.value === InvoiceTypeEnum.ELECTRONIC_COMMON);
				}
				// 企业类型可以选择所有发票类型
				return INVOICE_TYPE_OPTIONS;
			}),

			// 显示用的订单列表（确保至少有一个初始项）
			displayOrderList: computed(() => {
				if (state.formData.selectedOrderList.length === 0) {
					return [0]; // 当数组为空时，显示一个初始项
				}
				return state.formData.selectedOrderList;
			}),
			// 发票的额外验证规则
			specialInvoiceRules: computed(() => {
				return {
					// 开票金额校验（仅老订单时校验）
					invoiceAmount: [
						{
							required: state.isOldOrder,
							trigger: ['change', 'blur'],
							validator(rule: any, value: number) {
								if (!state.isOldOrder) {
									return Promise.resolve();
								}
								if (!value || value <= 0) {
									return Promise.reject('请输入有效的开票金额');
								}
								return Promise.resolve();
							}
						}
					],
					// 选择开票订单校验（仅老订单时校验 - 第一个项目）
					selectedOrderList: [
						{
							required: state.isOldOrder,
							validator(rule: any, value: number) {
								if (!state.isOldOrder) {
									return Promise.resolve();
								}
								if (!value || value === 0) {
									return Promise.reject('请选择开票订单');
								}
								return Promise.resolve();
							}
						}
					],
					// 选择开票订单校验（仅老订单时校验 - 其他项目）
					selectedOrderItem: [
						{
							required: state.isOldOrder,
							validator(rule: any, value: number) {
								if (!state.isOldOrder) {
									return Promise.resolve();
								}
								if (!value || value === 0) {
									return Promise.reject('请选择开票订单');
								}
								return Promise.resolve();
							}
						}
					],
					buyerBankName: [
						{
							required: computeds.isSpecialInvoice.value,
							trigger: ['change', 'blur'],
							validator(rule: any, value: string) {
								if (!value && computeds.isSpecialInvoice.value) {
									return Promise.reject('请输入开户银行');
								}
								if (value && !BANK_NAME_PATTERN.test(value)) {
									return Promise.reject('开户银行只能包含中文、英文和常用符号');
								}
								return Promise.resolve();
							}
						}
					],
					buyerBankAccount: [
						{
							required: computeds.isSpecialInvoice.value,
							trigger: ['change', 'blur'],
							validator(rule: any, value: string) {
								if (!value && computeds.isSpecialInvoice.value) {
									return Promise.reject('请输入银行账号');
								}
								if (value && !BANK_ACCOUNT_PATTERN.test(value)) {
									return Promise.reject('银行账号只能包含英文和数字');
								}
								return Promise.resolve();
							}
						}
					],
					buyerAddress: [
						{
							required: computeds.isSpecialInvoice.value,
							validator(rule: any, value: string) {
								if (!value && computeds.isSpecialInvoice.value) {
									return Promise.reject('请输入企业地址');
								}
								return Promise.resolve();
							}
						},
						{ max: MAX_ADDRESS_LENGTH, message: `企业地址不能超过${MAX_ADDRESS_LENGTH}个字符` }
					],
					buyerMobileNum: [
						{
							required: computeds.isSpecialInvoice.value,
							trigger: ['change', 'blur'],
							validator(rule: any, value: string) {
								console.log(value, computeds.isSpecialInvoice.value);
								if (!value) {
									if (computeds.isSpecialInvoice.value) {
										return Promise.reject('请输入企业电话');
									} else {
										return Promise.resolve();
									}
								}

								if (!PHONE_PATTERN.test(value)) {
									// 测试电话
									if (PhoneRegExp.test(value)) {
										return Promise.resolve();
									}

									return Promise.reject('请输入正确的电话号码格式（手机号11位或固定电话8位）');
								}

								return Promise.resolve();
							}
						}
					]
				};
			})
		};

		// 表单验证规则
		const formRules = {
			receiveEmail: [
				{ required: true, message: '请输入交付邮箱' },
				{ pattern: EMAIL_PATTERN, message: '请输入正确的邮箱格式' }
			],
			invoiceType: [{ required: true, message: '请选择发票类型' }]
		};

		const constants = {
			HEADER_TYPE_OPTIONS,
			INVOICE_TYPE_OPTIONS,
			MAX_ADDRESS_LENGTH
		};

		const methods = {
			// 显示弹窗
			show(orderNos: string[], invoiceBaseInfo: ApplyInvoicePreviewResponse) {
				state.orderNos = orderNos;
				state.isOldOrder = false;

				// 设置表单初始值
				state.formData = Object.assign({}, state.formData, {
					// 发票抬头
					buyerName: invoiceBaseInfo.buyerName,
					// 税号
					buyerIdNum: invoiceBaseInfo.buyerIdNum,
					// 开票金额
					invoiceAmount: invoiceBaseInfo.invoiceAmount,
					// 重置选择的订单列表
					selectedOrderList: [],
					// 默认选择企业抬头
					buyerType: BuyerTypeEnum.COMPANY,
					// 默认选择普通发票
					invoiceType: InvoiceTypeEnum.ELECTRONIC_COMMON
				});

				state.visible = true;
			},

			// 老订单弹窗
			showOldOrder() {
				state.orderNos = [];
				state.isOldOrder = true;

				methods.getOldOrderList();

				state.visible = true;
			},

			// 获取历史可开票订单
			getOldOrderList() {
				GetHistoryOrderListStore.request()
					.getData()
					.then(data => {
						console.log(data);
						state.historyOrderList = data;
					});
			},

			/**
			 * 处理订单选择变化
			 * @param index 索引
			 * @param value 选择的值
			 */
			handleOrderSelectChange(index: number, value: number) {
				// 如果数组为空，先初始化
				if (state.formData.selectedOrderList.length === 0) {
					state.formData.selectedOrderList = [value];
				} else {
					// 确保数组有足够的长度
					while (state.formData.selectedOrderList.length <= index) {
						state.formData.selectedOrderList.push(0);
					}
					state.formData.selectedOrderList[index] = value;
				}
			},

			/**
			 * 添加开票订单下拉框
			 */
			addOrderSelectItem() {
				// 如果是空数组，先添加第一个项，再添加新项
				if (state.formData.selectedOrderList.length === 0) {
					state.formData.selectedOrderList.push(0);
				}
				state.formData.selectedOrderList.push(0);
			},

			/**
			 * 删除开票订单下拉框
			 * @param index 要删除的索引
			 */
			removeOrderSelectItem(index: number) {
				// 如果删除后会变成空数组，则保留一个空项
				if (state.formData.selectedOrderList.length > 1) {
					state.formData.selectedOrderList.splice(index, 1);
				} else {
					// 如果只有一个项，清空数组（通过计算属性会自动显示一个初始项）
					state.formData.selectedOrderList = [];
				}
			},

			// 发票类型变化时的处理
			onInvoiceTypeChange() {
				// 切换到普通发票时，清空专用发票的必填字段验证错误
				if (state.formData.invoiceType === InvoiceTypeEnum.ELECTRONIC_COMMON) {
					formRef.value?.clearValidate([
						'buyerBankName',
						'buyerBankAccount',
						'buyerAddress',
						'buyerMobileNum'
					]);
				}
			},

			// 提交表单
			async onSubmit() {
				try {
					// 表单验证
					await formRef.value?.validate();

					state.submitLoading = true;

					// 构建提交数据
					const submitData: InvoiceApplicationFormModel = MUtils.unDataProxy({
						...state.formData,
						orderNos: state.orderNos
					});

					console.log('选择的开票订单:', state.formData.selectedOrderList);
					console.log('开票金额:', state.formData.invoiceAmount);

					// 如果是普通发票，清空专用发票的字段
					// if (submitData.invoiceType === InvoiceTypeEnum.ELECTRONIC_COMMON) {
					// 	submitData.buyerBankName = null;
					// 	submitData.buyerBankAccount = null;
					// 	submitData.buyerAddress = null;
					// 	submitData.buyerMobileNum = null;
					// }

					console.log('开票申请数据:', submitData);

					const response = await SubmitInvoiceApplicationStore.request(submitData).getData();

					if (response?.serialNo) {
						methods.onClose();

						emit('success');

						MUtils.confirm({
							title: '提交反馈',
							content: '本次开票申请已提交成功，请耐心等待审核！',
							confirmText: '我知道了'
						});
					}
				} catch (error) {
					console.error('开票申请提交失败:', error);
				} finally {
					state.submitLoading = false;
				}
			},

			// 关闭弹窗
			onClose() {
				state.visible = false;
				state.submitLoading = false;

				// 重置表单
				formRef.value?.resetFields();

				emit('close');
			}
		};

		return {
			formRef,
			...toRefs(state),
			...computeds,
			...constants,
			...methods,
			formRules
		};
	}
});
