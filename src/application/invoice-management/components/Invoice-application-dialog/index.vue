<template>
	<pm-dialog v-model:visible="visible" title="开票申请" width="420px" centered @close="onClose" destroy-on-close>
		<div class="invoice-application-dialog">
			<m-form
				ref="formRef"
				:model="formData"
				:rules="formRules"
				:label-col="{ span: 24 }"
				:wrapper-col="{ span: 24 }"
				layout="vertical"
				@finish="onSubmit"
			>
				<div v-if="isOldOrder">
					<!-- 开票订单选择验证包装 -->
					<m-form-item name="selectedOrderList" :rules="specialInvoiceRules.selectedOrderList">
						<template #label>选择开票订单</template>
						<div>
							<div
								v-for="(orderItem, index) in formData.selectedOrderList"
								:key="index"
								class="invoice-order-item"
							>
								<div class="invoice-order-row">
									<m-select
										v-model:value="formData.selectedOrderList[index]"
										:options="oldOrderList"
										placeholder="请选择开票订单"
										class="invoice-order-select"
									/>
									<m-button
										v-if="formData.selectedOrderList.length > 1"
										type="text"
										danger
										@click="removeOrderSelectItem(index)"
										class="remove-btn"
									>
										删除
									</m-button>
								</div>
							</div>
							<div>
								<m-button type="dashed" @click="addOrderSelectItem" class="add-btn">
									<template #icon>
										<plus-outlined />
									</template>
									添加开票订单
								</m-button>
							</div>
						</div>
					</m-form-item>
				</div>

				<!-- 开票金额 -->
				<m-form-item
					:label="isOldOrder ? '申请开票金额' : '开票金额'"
					name="invoiceAmount"
					:rules="specialInvoiceRules.invoiceAmount"
				>
					<m-input-number
						v-model:value="formData.invoiceAmount"
						prefix="￥"
						:min="0"
						:max="100"
						:disabled="!isOldOrder"
						:controls="false"
						class="amount-input"
					/>
				</m-form-item>

				<!-- 抬头类型 -->
				<m-form-item label="抬头类型" name="buyerType" v-if="!isOldOrder">
					<m-input
						:value="HEADER_TYPE_OPTIONS.find(option => option.value === formData.buyerType)?.label"
						disabled
						class="amount-input"
					></m-input>
				</m-form-item>

				<!-- 发票类型 -->
				<m-form-item label="发票类型" name="invoiceType">
					<m-radio-group
						v-model:value="formData.invoiceType"
						:options="availableInvoiceTypes"
						@change="onInvoiceTypeChange"
					/>
				</m-form-item>

				<!-- 发票抬头 -->
				<m-form-item label="发票抬头" name="buyerName">
					<m-input v-model:value="formData.buyerName" disabled placeholder="发票抬头" />
				</m-form-item>

				<!-- 税号 -->
				<m-form-item label="税号" name="taxNumber">
					<m-input v-model:value="formData.buyerIdNum" disabled placeholder="税号" />
				</m-form-item>

				<!-- 交付邮箱 -->
				<m-form-item label="交付邮箱" name="receiveEmail">
					<m-input v-model:value="formData.receiveEmail" :maxlength="64" placeholder="请输入交付邮箱" />
				</m-form-item>

				<!-- 开户银行 -->
				<m-form-item label="开户银行" name="buyerBankName" :rules="specialInvoiceRules.buyerBankName">
					<m-input v-model:value="formData.buyerBankName" :maxlength="100" placeholder="请输入开户银行" />
				</m-form-item>

				<!-- 银行账号 -->
				<m-form-item label="银行账号" name="buyerBankAccount" :rules="specialInvoiceRules.buyerBankAccount">
					<m-input v-model:value="formData.buyerBankAccount" :maxlength="100" placeholder="请输入银行账号" />
				</m-form-item>

				<!-- 企业地址 -->
				<m-form-item label="企业地址" name="buyerAddress" :rules="specialInvoiceRules.buyerAddress">
					<m-textarea
						v-model:value="formData.buyerAddress"
						placeholder="请输入企业地址"
						:maxlength="MAX_ADDRESS_LENGTH"
						:show-count="true"
						:rows="3"
					/>
				</m-form-item>

				<!-- 企业电话 (专用发票必填) -->
				<m-form-item label="企业电话" name="buyerMobileNum" :rules="specialInvoiceRules.buyerMobileNum">
					<m-input v-model:value="formData.buyerMobileNum" placeholder="请输入企业电话" />
				</m-form-item>
			</m-form>
		</div>
		<template #footer>
			<m-button @click="onClose">取消</m-button>
			<m-button @click="onSubmit" type="primary" html-type="submit" :loading="submitLoading">提交</m-button>
		</template>
	</pm-dialog>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
