import type { InvoiceApplicationFormModel } from '@/application/invoice-management/types';

export interface StateModel {
	visible: boolean;
	submitLoading: boolean;
	// 订单号列表
	orderNos: string[];
	// 是否是老订单
	isOldOrder: boolean;
	historyOrderList: HistoryOrderResponse[];
	// 表单数据
	formData: InvoiceApplicationFormModel;
}

export interface HistoryOrderResponse {
	/**
	* 账户明细Id
	*/
	id: number;

	/**
	 * 订单号
	 */
	orderNo: string;


	/**
	 * 商品名称
	 */
	goodsName: string;

	/**
	 * 备注
	 */
	comment: string;

	/**
	 * 订单金额
	 */
	orderAmount: number;

	/**
	 * 可开票金额
	 */
	invoiceableAmount: number;

	/**
	 * 能否申请开蓝票
	 */
	enableApplyBlueInvoice: boolean;

	/**
	 * 支付时间
	 */
	paidTime: string;

	/**
	 * 下单时间
	 */
	createTime: string;

	/**
	 * 退单金额
	 */
	refundAmount: number;
}
