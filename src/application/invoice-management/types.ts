/**
 * 发票管理页面类型定义
 */
import { InvoiceStatusEnum, BuyerTypeEnum, InvoiceTypeEnum } from './constants';

/**
 * 筛选条件接口
 */
export interface FilterParamsModel {
	/** 订单时间范围 */
	orderTimeRange?: [string, string];
	/** 发票状态 */
	invoiceStatus?: InvoiceStatusEnum;
}

/**
 * 消费记录接口
 */
export interface ConsumptionRecordModel {
	/** 订单号 */
	orderNo: string;
	/** 消费时间 */
	transactionTime: number;
	/** 商品名称 */
	goodsName: string;
	/** 交易金额 */
	tradeAmount: number;
	/** 退单金额 */
	refundAmount: number;
	/** 可开票金额 */
	invoiceableAmount: number;
	/** 已开票金额 */
	invoicedAmount: number;
	/** 红冲金额 */
	redFlushAmount: number;
	/** 开票状态 */
	invoiceStatus: number;
	/** 开票状态描述 */
	invoiceStatusDesc: string;
	/** 是否可以申请开票 */
	enableApplyBlueInvoice: boolean;
}

/**
 * 分页参数接口
 */
export interface PaginationParamsModel {
	current: number;
	pageSize: number;
}

/**
 * 列表查询参数接口
 */
export interface ListParamsModel extends FilterParamsModel, PaginationParamsModel {
	sortBy?: string;
	sortOrder?: 'asc' | 'desc';
}

/**
 * 分页响应接口
 */
export interface PaginationResponseModel<T> {
	list: T[];
	total: number;
	current: number;
	pageSize: number;
}

/**
 * 开票申请表单接口
 */
export interface InvoiceApplicationFormModel {
	/** 开票金额（分） */
	invoiceAmount: number;
	/** 选择的开票订单列表 */
	selectedOrderList: number[];
	/** 抬头类型 */
	buyerType: BuyerTypeEnum;
	/** 发票类型 */
	invoiceType: InvoiceTypeEnum;
	/** 发票抬头 */
	buyerName: string;
	/** 税号 */
	buyerIdNum: string;
	/** 交付邮箱 */
	receiveEmail: string;
	/** 开户银行 */
	buyerBankName?: string;
	/** 银行账号 */
	buyerBankAccount?: string;
	/** 企业地址 */
	buyerAddress?: string;
	/** 企业电话 */
	buyerMobileNum?: string;
	/** 记录ID列表 */
	orderNos: string[];
}

/**
 * 发票抬头信息接口
 */
export interface ApplyInvoicePreviewResponse {
	/** 开票金额 */
	invoiceAmount: number;
	/** 发票抬头 */
	buyerName: string;
	/** 税号 */
	buyerIdNum: string;
}

/**
 * 红冲发票表单接口
 */
export interface RedInvoiceFormModel {
	/** 发票代码 */
	invoiceCode: string;
	/** 发票号码 */
	invoiceNumber: string;
	/** 红冲金额（分） */
	redAmount: number;
	/** 红冲原因 */
	reason: string;
	/** 原发票ID */
	originalInvoiceId: string;
}

/**
 * 发票记录接口
 */
export interface InvoiceRecordResponse {
	/** 开票流水号 */
	serialNo: string;
	/** 申请开票时间 */
	applyTime: number;
	/** 开票详情 */
	invoiceDetail: string;
	/** 开票类型 */
	issuingTypes: string[];
	/** 开票状态 */
	invoiceStatus: number;
	/** 开票展示状态 */
	invoiceShowStatus: string;
	/** 开票成功时间 */
	invoiceSuccessTime: number;
	/** 开票金额 */
	invoiceAmount: number;
	/** 红冲金额 */
	redFlushAmount: number;
}

/**
 * 表单验证规则接口
 */
export interface FormRuleModel {
	required?: boolean;
	message?: string;
	pattern?: RegExp;
	validator?: (rule: any, value: any, callback: any) => void;
}

/**
 * 表单验证规则集合接口
 */
export interface FormRulesModel {
	[key: string]: FormRuleModel[];
}

/**
 * 企业认证信息接口
 */
export interface CompanyCertificationInfoModel {
	/** 企业名称 */
	companyName: string;
	/** 统一社会信用代码 */
	uscc: string;
	/** 是否为授权个人分校 */
	isAuthorizedPersonalBranch: boolean;
	/** 授权主体信息 */
	authorizingEntityInfo?: {
		/** 授权主体企业名称 */
		companyName: string;
		/** 授权主体统一社会信用代码 */
		uscc: string;
	};
}

/**
 * 发票相关类型
 */
export type InvoiceStatusModel = '待开票' | '开票中' | '已开票' | '部分红冲' | '全部红冲';

export interface InvoiceItemModel {
	id: string; // 假设有个唯一ID
	orderTime: string; // 订单消费时间
	productName: string; // 商品名称
	transactionAmount: number; // 交易金额
	invoiceableAmount: number; // 可开票金额
	invoiceStatus: InvoiceStatusModel; // 发票状态
	// 根据需要添加其他字段
}

/** 发票预览 */
export interface InvoicePreviewResponse {
	/** 发票流水号 */
	serialNo: string;
	/** 发票预览地址 */
	invoicePreviewUrl: string;
	/** 开票类型 */
	issuingType: number;
	/** 开票类型 */
	issuingTypeDesc: string;
	/** 开票状态 */
	invoiceStatus: number;
	/** 开票状态 */
	invoiceStatusDesc: string;
}
