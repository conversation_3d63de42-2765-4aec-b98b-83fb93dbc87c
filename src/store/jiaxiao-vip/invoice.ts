import { AgentStore, MethodTypeModel } from '@paas/paas-library';

/**
 * 获取可开票订单列表
 */
export class InvoiceableOrdersListStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/invoice/query-invoice-order.htm';
	method: MethodTypeModel = 'GET';
}

/**
 * 获取发票记录列表
 */
export class InvoiceRecordListStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/invoice/query-invoice.htm';
	method: MethodTypeModel = 'GET';
}

/**
 * 提交开票申请
 */
export class InvoiceApplicationStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/invoice/apply-invoice.htm';
	method: MethodTypeModel = 'POST';
}

/**
 * 获取驾校申请开票信息
 */
export class ApplyInvoicePreviewStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/invoice/apply-invoice-preview.htm';
	method: MethodTypeModel = 'GET';
}

/**
 * 获取是否显示开票按钮
 */
export class IsShowInvoiceButtonStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/invoice/is-show-invoice-button.htm';
	method: MethodTypeModel = 'GET';
}

/**
 * 获取发票预览信息
 */
export class InvoicePreviewStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/invoice/preview-invoices.htm';
	method: MethodTypeModel = 'GET';
}

/**
 * 历史订单申请开蓝票
 */
export class HistoryOrderInvoiceStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/invoice/apply-history-order-invoice.htm';
	method: MethodTypeModel = 'GET';
}

/**
 * 可开票的历史订单列表接口
 */
export class HistoryOrderListStore<T> extends AgentStore<T> {
	url = 'jiaxiao-vip://api/web/admin/account/list-invoicable-order.htm';
	method: MethodTypeModel = 'GET';
}