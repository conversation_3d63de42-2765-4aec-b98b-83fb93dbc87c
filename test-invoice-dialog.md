# 开票申请对话框功能测试

## 修改内容总结

### 1. 数据结构修改
- 将 `formData.invoiceAmount` 改为 `formData.invoiceAmountList: number[]`
- 默认初始化为 `[0]`，包含一个默认项

### 2. 模板修改
- 使用 `v-for` 循环渲染多个下拉框
- 每个下拉框都有对应的删除按钮（当数量大于1时显示）
- 添加了"添加开票订单"按钮

### 3. 功能方法
- `addInvoiceAmountItem()`: 添加新的下拉框项
- `removeInvoiceAmountItem(index)`: 删除指定索引的下拉框项

### 4. 样式优化
- 添加了响应式布局样式
- 美化了添加按钮和删除按钮的外观

### 5. 表单验证
- 更新了验证规则以支持数组形式的字段

## 测试要点

1. **基本功能测试**
   - 点击"添加开票订单"按钮，应该新增一个下拉框
   - 点击删除按钮，应该删除对应的下拉框
   - 当只有一个下拉框时，删除按钮应该隐藏

2. **表单验证测试**
   - 提交表单时应该验证所有下拉框的值
   - 空值应该显示验证错误

3. **数据提交测试**
   - 提交时应该正确处理数组数据
   - 后端接口应该能正常接收数据

## 注意事项

- 图标组件 `PlusOutlined` 已正确导入和注册
- 样式文件已更新，支持新的布局
- 保持了原有的业务逻辑不变
